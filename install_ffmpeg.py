#!/usr/bin/env python3
"""
FFmpeg Installer

A standalone script to install FFmpeg on Windows, macOS, and Linux systems.
This script detects your operating system and installs FFmpeg using the appropriate method.

Usage:
    python install_ffmpeg.py
"""

import os
import sys
import shutil
import platform
import tempfile
import zipfile
import subprocess
from pathlib import Path
import argparse

# Try to import rich for better console output, but fallback to simple print statements if not available
try:
    from rich.console import Console
    from rich.progress import Progress, SpinnerColumn, TimeElapsedColumn, TextColumn, BarColumn, TaskProgressColumn
    from rich.prompt import Confirm
    RICH_AVAILABLE = True
    console = Console()
except ImportError:
    RICH_AVAILABLE = False

# Try to import requests, but provide helpful error if not available
try:
    import requests
except ImportError:
    if RICH_AVAILABLE:
        console.print("[bold red]Error: The 'requests' library is required for this script.[/bold red]")
        console.print("[yellow]Please install it with: pip install requests[/yellow]")
    else:
        print("Error: The 'requests' library is required for this script.")
        print("Please install it with: pip install requests")
    sys.exit(1)


def print_status(message, status="info"):
    """Print a status message with appropriate formatting based on available libraries."""
    if RICH_AVAILABLE:
        if status == "info":
            console.print(f"[cyan]{message}[/cyan]")
        elif status == "success":
            console.print(f"[bold green]{message}[/bold green]")
        elif status == "warning":
            console.print(f"[bold yellow]{message}[/bold yellow]")
        elif status == "error":
            console.print(f"[bold red]{message}[/bold red]")
    else:
        print(message)


def check_ffmpeg_installed():
    """Check if FFmpeg is installed and available in the system path."""
    return shutil.which('ffmpeg') is not None


def install_ffmpeg(auto_confirm=False):
    """
    Attempts to install FFmpeg based on the detected operating system.
    Returns True if successful, False otherwise.
    
    Args:
        auto_confirm: If True, skips confirmation prompts
    """
    system = platform.system().lower()
    
    print_status(f"FFmpeg is not installed. Attempting to install it automatically...", "warning")
    
    if system == "windows":
        return install_ffmpeg_windows(auto_confirm)
    elif system == "darwin":  # macOS
        return install_ffmpeg_macos(auto_confirm)
    elif system == "linux":
        return install_ffmpeg_linux(auto_confirm)
    else:
        print_status(f"Automatic installation not supported for {platform.system()}.", "error")
        print_status("Please install FFmpeg manually: https://ffmpeg.org/download.html", "warning")
        return False


def install_ffmpeg_windows(auto_confirm=False):
    """
    Install FFmpeg on Windows by downloading and extracting the binaries.
    
    Args:
        auto_confirm: If True, skips confirmation prompts
    """
    try:
        # Define the FFmpeg download URL
        ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        
        # Create progress indicator if rich is available
        if RICH_AVAILABLE:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
                TimeElapsedColumn(),
                console=console,
            ) as progress:
                download_task = progress.add_task("Downloading FFmpeg...", total=1000)
                
                # Create temp directory
                temp_dir = tempfile.mkdtemp()
                zip_path = os.path.join(temp_dir, "ffmpeg.zip")
                
                # Download FFmpeg
                response = requests.get(ffmpeg_url, stream=True)
                total_size = int(response.headers.get('content-length', 0))
                
                with open(zip_path, 'wb') as f:
                    if total_size == 0:
                        f.write(response.content)
                    else:
                        downloaded = 0
                        for data in response.iter_content(chunk_size=4096):
                            downloaded += len(data)
                            f.write(data)
                            progress.update(download_task, completed=int(downloaded / total_size * 1000))
                
                # Extract the zip file
                progress.update(download_task, description="Extracting FFmpeg...", completed=1000)
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                
                # Find the bin directory in the extracted folder
                bin_dir = None
                for root, dirs, _ in os.walk(temp_dir):
                    if 'bin' in dirs:
                        bin_dir = os.path.join(root, 'bin')
                        break
                
                if not bin_dir:
                    print_status("Failed to find FFmpeg binaries in the downloaded package.", "error")
                    return False
                
                # Create the installation directory
                install_dir = os.path.join(os.path.expanduser("~"), ".ffmpeg")
                if not os.path.exists(install_dir):
                    os.makedirs(install_dir)
                
                # Copy the FFmpeg binaries to the installation directory
                progress.update(download_task, description="Installing FFmpeg...", completed=500)
                
                for file in os.listdir(bin_dir):
                    if file.endswith('.exe'):
                        shutil.copy2(os.path.join(bin_dir, file), os.path.join(install_dir, file))
                
                # Add to PATH for current session
                os.environ["PATH"] += os.pathsep + install_dir
                
                # Check if it worked
                if not shutil.which('ffmpeg'):
                    print_status("Failed to add FFmpeg to PATH.", "error")
                    return False
                
                progress.update(download_task, description="FFmpeg installed successfully!", completed=1000)
        else:
            # Simplified version without rich progress bar
            print_status("Downloading FFmpeg...")
            temp_dir = tempfile.mkdtemp()
            zip_path = os.path.join(temp_dir, "ffmpeg.zip")
            
            # Download FFmpeg
            response = requests.get(ffmpeg_url)
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            print_status("Extracting FFmpeg...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # Find the bin directory
            bin_dir = None
            for root, dirs, _ in os.walk(temp_dir):
                if 'bin' in dirs:
                    bin_dir = os.path.join(root, 'bin')
                    break
            
            if not bin_dir:
                print_status("Failed to find FFmpeg binaries in the downloaded package.", "error")
                return False
            
            # Create the installation directory
            install_dir = os.path.join(os.path.expanduser("~"), ".ffmpeg")
            if not os.path.exists(install_dir):
                os.makedirs(install_dir)
            
            print_status("Installing FFmpeg...")
            for file in os.listdir(bin_dir):
                if file.endswith('.exe'):
                    shutil.copy2(os.path.join(bin_dir, file), os.path.join(install_dir, file))
            
            # Add to PATH for current session
            os.environ["PATH"] += os.pathsep + install_dir
            
            # Check if it worked
            if not shutil.which('ffmpeg'):
                print_status("Failed to add FFmpeg to PATH.", "error")
                return False
            
            print_status("FFmpeg installed successfully!", "success")
            
        # Instructions for permanent PATH addition
        print_status("\nFFmpeg has been installed to: " + install_dir, "success")
        print_status("To make FFmpeg available permanently, add this directory to your system PATH:", "warning")
        print_status("1. Right-click on 'This PC' or 'My Computer' and select 'Properties'", "warning")
        print_status("2. Click on 'Advanced system settings'", "warning")
        print_status("3. Click on 'Environment Variables'", "warning")
        print_status("4. Under 'System variables', find and select 'Path', then click 'Edit'", "warning")
        print_status(f"5. Click 'New' and add: {install_dir}", "warning")
        print_status("6. Click 'OK' on all dialogs", "warning")
        
        return True
        
    except Exception as e:
        print_status(f"Error installing FFmpeg: {str(e)}", "error")
        return False


def install_ffmpeg_macos(auto_confirm=False):
    """
    Install FFmpeg on macOS using Homebrew.
    
    Args:
        auto_confirm: If True, skips confirmation prompts
    """
    try:
        # Check if Homebrew is installed
        if not shutil.which('brew'):
            print_status("Homebrew is required to install FFmpeg on macOS.", "warning")
            print_status("Install Homebrew with:", "warning")
            print_status('/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"', "info")
            
            if not auto_confirm:
                if RICH_AVAILABLE:
                    if not Confirm.ask("Would you like to install Homebrew now?"):
                        return False
                else:
                    response = input("Would you like to install Homebrew now? (y/n): ")
                    if response.lower() != 'y':
                        return False
            
            print_status("Installing Homebrew...", "info")
            subprocess.run(['/bin/bash', '-c', '$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)'], check=True, shell=True)
        
        # Install FFmpeg
        print_status("Installing FFmpeg with Homebrew...", "info")
        subprocess.run(['brew', 'install', 'ffmpeg'], check=True)
        
        # Verify installation
        if shutil.which('ffmpeg'):
            print_status("FFmpeg installed successfully!", "success")
            return True
        else:
            print_status("FFmpeg installation failed.", "error")
            return False
            
    except Exception as e:
        print_status(f"Error installing FFmpeg: {str(e)}", "error")
        return False


def install_ffmpeg_linux(auto_confirm=False):
    """
    Install FFmpeg on Linux using the appropriate package manager.
    
    Args:
        auto_confirm: If True, skips confirmation prompts
    """
    try:
        # Detect package manager
        if shutil.which('apt-get'):  # Debian, Ubuntu
            package_manager = ('apt-get', 'update', 'install')
            need_sudo = True
        elif shutil.which('dnf'):    # Fedora
            package_manager = ('dnf', '-y', 'install')
            need_sudo = True
        elif shutil.which('yum'):    # CentOS, RHEL
            package_manager = ('yum', '-y', 'install')
            need_sudo = True
        elif shutil.which('pacman'): # Arch Linux
            package_manager = ('pacman', '-Sy', '--noconfirm')
            need_sudo = True
        else:
            print_status("Could not detect a supported package manager.", "error")
            print_status("Please install FFmpeg manually for your distribution.", "warning")
            return False
        
        # Confirm with user if sudo is needed
        if need_sudo and not auto_confirm:
            print_status(f"Installing FFmpeg with {package_manager[0]} requires administrative privileges.", "warning")
            if RICH_AVAILABLE:
                if not Confirm.ask("Do you want to continue?"):
                    return False
            else:
                response = input("Do you want to continue? (y/n): ")
                if response.lower() != 'y':
                    return False
        
        # Prepare the command with sudo if needed
        sudo_prefix = ["sudo"] if need_sudo else []
        
        # Update package lists if needed
        if package_manager[0] in ('apt-get', 'pacman'):
            print_status(f"Updating package lists with {package_manager[0]}...", "info")
            subprocess.run(sudo_prefix + [package_manager[0], package_manager[1]], check=True)
        
        # Install FFmpeg
        print_status(f"Installing FFmpeg with {package_manager[0]}...", "info")
        if package_manager[0] == 'pacman':
            subprocess.run(sudo_prefix + [package_manager[0], package_manager[1], package_manager[2], 'ffmpeg'], check=True)
        else:
            subprocess.run(sudo_prefix + [package_manager[0], package_manager[2], 'ffmpeg'], check=True)
        
        # Verify installation
        if shutil.which('ffmpeg'):
            print_status("FFmpeg installed successfully!", "success")
            return True
        else:
            print_status("FFmpeg installation failed.", "error")
            return False
            
    except Exception as e:
        print_status(f"Error installing FFmpeg: {str(e)}", "error")
        return False


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Install FFmpeg on Windows, macOS, or Linux systems."
    )
    parser.add_argument(
        "--yes", "-y", 
        action="store_true", 
        help="Automatically confirm all prompts"
    )
    return parser.parse_args()


def main():
    """Main entry point for the script."""
    args = parse_args()
    
    if check_ffmpeg_installed():
        print_status("FFmpeg is already installed!", "success")
        try:
            # Get the path to the FFmpeg executable
            ffmpeg_path = shutil.which('ffmpeg')
            # Get the version information
            result = subprocess.run([ffmpeg_path, "-version"], capture_output=True, text=True, check=True)
            print_status(f"Installed at: {ffmpeg_path}", "info")
            print_status(f"Version: {result.stdout.splitlines()[0]}", "info")
            return 0
        except Exception as e:
            print_status(f"Error getting FFmpeg version: {str(e)}", "error")
            return 1
    
    # Confirm installation if not using --yes
    if not args.yes:
        if RICH_AVAILABLE:
            if not Confirm.ask("FFmpeg is not installed. Would you like to install it now?"):
                return 1
        else:
            response = input("FFmpeg is not installed. Would you like to install it now? (y/n): ")
            if response.lower() != 'y':
                return 1
    
    # Install FFmpeg
    success = install_ffmpeg(auto_confirm=args.yes)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main()) 